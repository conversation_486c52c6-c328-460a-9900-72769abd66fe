package com.ehome.oc.controller.wx;

import com.ehome.common.core.controller.BaseWxController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.utils.StringUtils;
import com.ehome.oc.domain.PlateRecognitionLogRecord;
import com.ehome.oc.service.PlateRecognitionService;
import com.ehome.oc.service.PlateRecognitionLogService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 小程序车辆相关控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/wx/vehicle")
public class WxVehicleController extends BaseWxController {
    
    private static final Logger logger = LoggerFactory.getLogger(WxVehicleController.class);
    
    @Autowired
    private PlateRecognitionService plateRecognitionService;

    @Autowired
    private PlateRecognitionLogService plateRecognitionLogService;
    
    /**
     * 车牌识别接口
     */
    @PostMapping("/recognizePlate")
    public AjaxResult recognizePlate(@RequestBody Map<String, String> params) {
        long startTime = System.currentTimeMillis();
        PlateRecognitionLogRecord logRecord = null;

        try {
            // 检查用户登录状态
            if (getCurrentUser() == null) {
                return AjaxResult.error("用户未登录");
            }

            // 获取fileId参数
            String fileId = params.get("fileId");
            if (StringUtils.isEmpty(fileId)) {
                return AjaxResult.error("文件ID不能为空");
            }

            // 创建识别记录
            logRecord = plateRecognitionLogService.createLogRecord(
                getCurrentUser().getCommunityId(),
                getCurrentUser().getUserId().toString(),
                getCurrentUser().getNickname(),
                getCurrentUser().getMobile()
            );
            logRecord.setFileId(fileId);
            logRecord.setIpAddress(getRequest().getRemoteAddr());

            logger.info("开始识别车牌，文件ID：{}", fileId);

            // 根据fileId获取文件信息
            Record fileInfo = Db.findFirst("SELECT * FROM eh_file_info WHERE file_id = ?", fileId);
            logger.info("查询文件信息：fileId={}, 查询结果={}", fileId, fileInfo != null ? "找到" : "未找到");

            if (fileInfo == null) {
                logRecord.setErrorMessage("文件不存在：" + fileId);
                plateRecognitionLogService.saveRecognitionLog(logRecord);
                return AjaxResult.error("文件不存在");
            }

            // 设置文件大小
            Long fileSize = fileInfo.getLong("file_size");
            if (fileSize != null) {
                logRecord.setImageSize(fileSize.intValue());
            }

            // 检查文件类型
            String fileType = fileInfo.getStr("file_type");
            String originalFilename = fileInfo.getStr("original_filename");
            logger.info("文件类型检查：fileId={}, fileType={}, originalFilename={}", fileId, fileType, originalFilename);

            if (!isImageFileType(fileType) && !isImageByExtension(originalFilename)) {
                logRecord.setErrorMessage("文件类型不支持：" + fileType);
                plateRecognitionLogService.saveRecognitionLog(logRecord);
                return AjaxResult.error("请上传图片文件（支持jpg、png、bmp格式）");
            }

            // 检查文件大小（限制为5MB）
            if (fileSize != null && fileSize > 5 * 1024 * 1024) {
                logRecord.setErrorMessage("文件大小超限：" + fileSize + " bytes");
                plateRecognitionLogService.saveRecognitionLog(logRecord);
                return AjaxResult.error("图片文件大小不能超过5MB");
            }

            // 获取文件路径并读取文件
            String absolutePath = fileInfo.getStr("absolute_path");
            if (StringUtils.isEmpty(absolutePath)) {
                logRecord.setErrorMessage("文件路径为空");
                plateRecognitionLogService.saveRecognitionLog(logRecord);
                return AjaxResult.error("文件路径错误");
            }

            // 调用车牌识别服务
            AjaxResult recognitionResult = plateRecognitionService.recognizePlateByPath(absolutePath);

            logger.info("车牌识别结果: {}", recognitionResult);

            // 记录识别耗时
            long recognitionTime = System.currentTimeMillis() - startTime;
            logRecord.setRecognitionTime((int) recognitionTime);

            if (recognitionResult.get("code").equals(200)) {
                // 识别成功
                Map<String, Object> data = (Map<String, Object>) recognitionResult.get("data");
                String plateNumber = (String) data.get("plateNumber");
                Double probability = (Double) data.get("probability");
                String plateColor = (String) data.get("color");

                // 更新记录信息
                logRecord.setRecognitionStatus(1);
                logRecord.setPlateNumber(plateNumber);
                logRecord.setConfidence(BigDecimal.valueOf(probability));
                logRecord.setPlateColor(plateColor);

                // 查询车主信息
                AjaxResult ownerResult = getOwnerByPlate(plateNumber);
                if (ownerResult.get("code").equals(200)) {
                    Map<String, Object> ownerInfo = (Map<String, Object>) ownerResult.get("data");
                    logRecord.setOwnerFound(1);
                    logRecord.setOwnerId((String) ownerInfo.get("ownerId"));
                    logRecord.setOwnerName((String) ownerInfo.get("ownerName"));
                    logRecord.setOwnerPhone((String) ownerInfo.get("ownerPhone"));
                    data.put("ownerInfo", ownerInfo);
                } else {
                    logRecord.setOwnerFound(0);
                }

                // 保存记录
                plateRecognitionLogService.saveRecognitionLog(logRecord);

                return AjaxResult.success("识别成功", data);
            } else {
                // 识别失败
                logRecord.setRecognitionStatus(0);
                logRecord.setErrorMessage((String) recognitionResult.get("msg"));
                plateRecognitionLogService.saveRecognitionLog(logRecord);
            }

            return recognitionResult;

        } catch (Exception e) {
            logger.error("车牌识别失败", e);

            // 记录异常
            if (logRecord != null) {
                logRecord.setRecognitionStatus(0);
                logRecord.setErrorMessage("系统异常：" + e.getMessage());
                logRecord.setRecognitionTime((int) (System.currentTimeMillis() - startTime));
                plateRecognitionLogService.saveRecognitionLog(logRecord);
            }

            return AjaxResult.error("车牌识别失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据车牌号查询车主信息
     */
    @GetMapping("/getOwnerByPlate")
    public AjaxResult getOwnerByPlate(@RequestParam("plateNumber") String plateNumber) {
        try {
            // 检查用户登录状态
            if (getCurrentUser() == null) {
                return AjaxResult.error("用户未登录");
            }
            
            if (StringUtils.isEmpty(plateNumber)) {
                return AjaxResult.error("车牌号不能为空");
            }
            
            String communityId = getCurrentUser().getCommunityId();
            if (StringUtils.isEmpty(communityId)) {
                return AjaxResult.error("未找到社区信息");
            }
            
            // 查询车辆信息
            String sql = "SELECT v.*, o.owner_name, o.mobile, o.house_info " +
                        "FROM eh_vehicle v " +
                        "LEFT JOIN eh_owner o ON v.owner_id = o.owner_id " +
                        "WHERE v.plate_no = ? AND v.community_id = ? AND v.check_status = 1";
            
            List<Record> vehicles = Db.find(sql, plateNumber, communityId);
            
            if (vehicles.isEmpty()) {
                return AjaxResult.error("未找到该车牌对应的车主信息");
            }
            
            // 如果有多个结果，取第一个
            Record vehicle = vehicles.get(0);
            
            // 构建返回数据
            Map<String, Object> ownerInfo = new HashMap<>();
            ownerInfo.put("plateNumber", vehicle.getStr("plate_no"));
            ownerInfo.put("ownerName", vehicle.getStr("owner_real_name"));
            ownerInfo.put("ownerPhone", vehicle.getStr("owner_phone"));
            ownerInfo.put("houseName", vehicle.getStr("house_info"));
            ownerInfo.put("parkingSpace", vehicle.getStr("parking_space"));
            ownerInfo.put("vehicleBrand", vehicle.getStr("vehicle_brand"));
            ownerInfo.put("vehicleModel", vehicle.getStr("vehicle_model"));
            
            logger.info("查询到车主信息：车牌号={}, 车主={}", plateNumber, vehicle.getStr("owner_real_name"));
            
            return AjaxResult.success("查询成功", ownerInfo);
            
        } catch (Exception e) {
            logger.error("查询车主信息失败：车牌号={}", plateNumber, e);
            return AjaxResult.error("查询车主信息失败：" + e.getMessage());
        }
    }
    
    /**
     * 检查文件类型是否为图片（基于file_type字段）
     */
    private boolean isImageFileType(String fileType) {
        if (StringUtils.isEmpty(fileType)) {
            return false;
        }
        String lowerFileType = fileType.toLowerCase();
        return lowerFileType.equals("jpg") || lowerFileType.equals("jpeg") ||
               lowerFileType.equals("png") || lowerFileType.equals("bmp");
    }

    /**
     * 根据文件扩展名判断是否为图片
     */
    private boolean isImageByExtension(String filename) {
        if (StringUtils.isEmpty(filename)) {
            return false;
        }
        String lowerFilename = filename.toLowerCase();
        return lowerFilename.endsWith(".jpg") || lowerFilename.endsWith(".jpeg") ||
               lowerFilename.endsWith(".png") || lowerFilename.endsWith(".bmp");
    }
}
