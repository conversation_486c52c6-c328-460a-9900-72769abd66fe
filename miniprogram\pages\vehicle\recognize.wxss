/* 车牌识别页面样式 */
.page-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #4A90E2 0%, #f8f9fa 30%);
}

/* 顶部标题区域 */
.header-section {
  padding: 40rpx 30rpx 60rpx;
  text-align: center;
}

.header-title {
  font-size: 44rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 16rpx;
}

.header-subtitle {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 主要内容区域 */
.main-content {
  padding: 0 30rpx;
  margin-top: -30rpx;
}

/* 卡片通用样式 */
.upload-card, .result-card {
  background: white;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.card-header {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: #f8f9fa;
  border-bottom: 1rpx solid #eee;
}

.card-icon {
  font-size: 36rpx;
  margin-right: 16rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 上传区域 */
.upload-area {
  padding: 40rpx 30rpx;
  text-align: center;
}

.uploaded-image {
  padding: 20rpx 30rpx;
}

/* 识别操作区域 */
.recognize-action {
  padding: 30rpx 0 10rpx;
  text-align: center;
}

.recognize-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 280rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #4A90E2 0%, #50C878 100%);
  color: white;
  border-radius: 40rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 600;
  box-shadow: 0 6rpx 16rpx rgba(74, 144, 226, 0.3);
  margin: 0 auto;
}

.recognize-btn .btn-icon {
  font-size: 30rpx;
  margin-right: 10rpx;
}

.recognize-btn .btn-text {
  font-size: 28rpx;
}

.upload-tips {
  margin-top: 30rpx;
  padding: 20rpx;
  background: #f0f8ff;
  border-radius: 12rpx;
}

.tip-item {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
  text-align: left;
}

.tip-item:last-child {
  margin-bottom: 0;
}

/* 识别进度 */
.recognition-progress {
  padding: 30rpx;
  text-align: center;
  background: #f8f9fa;
}

.progress-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.progress-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.progress-bar {
  width: 100%;
  height: 6rpx;
  background: #e0e0e0;
  border-radius: 3rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4A90E2, #50C878);
  border-radius: 3rpx;
  animation: progress 2s ease-in-out infinite;
}

@keyframes progress {
  0% { width: 0%; }
  50% { width: 70%; }
  100% { width: 100%; }
}

/* 车牌显示 */
.plate-display {
  padding: 30rpx;
  text-align: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.plate-number {
  font-size: 48rpx;
  font-weight: 700;
  letter-spacing: 4rpx;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.plate-confidence {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 车主信息 */
.owner-info {
  padding: 30rpx;
}

.owner-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.owner-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.owner-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.info-grid {
  margin-bottom: 30rpx;
}

.info-row {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.info-row:last-child {
  border-bottom: none;
}

.phone-row {
  background: #f8f9fa;
  margin: 0 -20rpx;
  padding: 20rpx;
  border-radius: 12rpx;
  border: none;
  cursor: pointer;
}

.info-label {
  font-size: 26rpx;
  color: #666;
  min-width: 140rpx;
}

.info-value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

.phone-value {
  color: #4A90E2;
  font-weight: 600;
}

.phone-icon {
  font-size: 28rpx;
  color: #4A90E2;
}

/* 操作按钮 */
.action-buttons {
  text-align: center;
}

.contact-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #4A90E2 0%, #50C878 100%);
  color: white;
  border-radius: 44rpx;
  border: none;
  font-size: 30rpx;
  font-weight: 600;
  box-shadow: 0 8rpx 20rpx rgba(74, 144, 226, 0.3);
}

.btn-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.btn-text {
  font-size: 30rpx;
}

/* 未找到车主信息 */
.no-owner-info {
  padding: 40rpx 30rpx;
  text-align: center;
}

.no-owner-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.no-owner-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
}

.no-owner-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 30rpx;
}

.retry-btn {
  width: 200rpx;
  height: 64rpx;
  line-height: 64rpx;
  background: #f0f0f0;
  color: #666;
  border-radius: 32rpx;
  font-size: 26rpx;
  border: none;
}

.van-uploader__upload{
  width:220rpx!important;
}